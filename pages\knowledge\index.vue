<template>
  <div class="knowledge-page-wrapper">
    <div>
      <!-- 整体内容容器 -->
      <div class="lawyer-content-wrapper">
        <!-- 搜索区域 -->
        <section>
          <h1>法规与文件大家智库</h1>
          <p>
            搜索和浏览法律法规、政策文件、典型案例和解读材料，获取最新的合规信息和专业指导。
          </p>

          <div class="lawyer-search-form">
            <a-input
              placeholder="输入关键词搜索法规、案例、解读..."
              size="large"
              v-model="searchText"
              class="lawyer-search-input"
              @keyup.enter="onSearch"
            />
            <a-button
              v-for="(btn, index) in searchButtons"
              :key="index"
              :type="btn.isActive ? 'primary' : btn.type || 'default'"
              :icon="btn.icon"
              size="large"
              :loading="btn.loading"
              @click="btn.handler"
              :class="{ 'lawyer-btn-active': btn.isActive }"
            >
              {{ btn.text }}{{ btn.count ? ` (${btn.count})` : "" }}
            </a-button>
          </div>

          <!-- 高级筛选选项 -->
          <div class="lawyer-filter-options" v-show="isAdvancedSearchVisible">
            <!-- 时效性选择器 -->
            <div class="lawyer-filter-group">
              <a-select
                v-model="timeLinessFilter"
                style="width: 100%"
                placeholder="时效性"
                @change="onSearch"
              >
                <a-select-option
                  v-for="option in timeLinessOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 效力位阶选择器 -->
            <div class="lawyer-filter-group">
              <a-select
                v-model="effectivenessLevelFilter"
                style="width: 100%"
                placeholder="效力位阶"
                @change="onSearch"
              >
                <a-select-option
                  v-for="option in effectivenessLevelOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 专题分类级联选择器 -->
            <div class="lawyer-filter-group">
              <a-cascader
                v-model="topicCategory"
                :options="topicCategoryOptions"
                placeholder="专题分类"
                style="width: 100%"
                @change="onSearch"
                :show-search="true"
              />
            </div>
            <!-- 来源筛选 -->
            <div class="lawyer-filter-group">
              <a-select
                v-model="filterSource"
                style="width: 100%"
                placeholder="全部来源"
                @change="onSearch"
              >
                <a-select-option value="all">全部来源</a-select-option>
                <a-select-option
                  v-for="option in websiteOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 排序方式 -->
            <div class="lawyer-filter-group">
              <a-select
                v-model="sortOrder"
                style="width: 100%"
                placeholder="排序方式"
                @change="onSearch"
              >
                <a-select-option value="desc"
                  >按发布日期 (新→旧)</a-select-option
                >
                <a-select-option value="asc"
                  >按发布日期 (旧→新)</a-select-option
                >
              </a-select>
            </div>
          </div>
        </section>

        <!-- 加载中 -->
        <div class="lawyer-loading-overlay" v-if="listLoading">
          <a-spin size="large" />
          <h3>正在努力加载中...</h3>
          <p>请稍候，我们正在为您检索信息。</p>
        </div>

        <!-- 无结果提示 -->
        <div class="lawyer-no-results" v-if="!listLoading && !documents.length">
          <h3>未能找到相关结果</h3>
          <p>请尝试调整您的搜索关键词或筛选条件。</p>
        </div>

        <!-- 文档列表 -->
        <div
          class="lawyer-document-list"
          v-if="!listLoading && documents.length"
        >
          <div
            class="lawyer-document-item"
            v-for="doc in documents"
            :key="doc.id"
          >
            <div class="lawyer-document-item-content">
              <div class="lawyer-document-icon">📄</div>
              <div class="lawyer-document-main-content">
                <div class="lawyer-document-header">
                  <h3 class="lawyer-document-title">
                    <nuxt-link :to="`/document/${doc.id}`">{{
                      doc.ruleName
                    }}</nuxt-link>
                  </h3>
                  <div class="lawyer-document-meta">
                    <span
                      ><a-icon type="calendar" /> {{ doc.publishDateStr }}</span
                    >
                    <span><a-icon type="bank" /> {{ doc.legalSource }}</span>
                    <span><a-icon type="eye" /> {{ doc.readCount }} 阅读</span>
                    <span class="lawyer-timeliness-tag">
                      <a-icon type="clock-circle" /> {{ doc.timeLiness }}
                    </span>
                  </div>
                </div>
                <p class="lawyer-document-summary">
                  {{ doc.fileContent || "暂无摘要" }}
                </p>
                <div class="lawyer-document-footer">
                  <div class="lawyer-document-tags">
                    <a-tag :color="getTagColor(doc.categoryMain, 'main')">{{
                      doc.categoryMain
                    }}</a-tag>
                    <a-tag
                      v-if="doc.categorySub"
                      :color="getTagColor(doc.categorySub, 'sub')"
                      >{{ doc.categorySub }}</a-tag
                    >
                  </div>
                  <div class="lawyer-document-actions">
                    <a-button
                      v-for="(action, index) in getDocActions(doc)"
                      :key="index"
                      :type="action.type || 'default'"
                      :class="action.class"
                      @click="action.handler"
                    >
                      {{ action.text }}
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="lawyer-pagination" v-if="documents.length">
            <a-pagination
              :current="currentPage"
              :total="totalDocuments"
              :pageSize="pageSize"
              show-size-changer
              show-quick-jumper
              @change="onPageChange"
              @showSizeChange="onShowSizeChange"
            />
          </div>
        </div>
      </div>

      <!-- 文件上传组件 -->
      <FileUploadModal
        :visible="uploadModalVisible"
        :title="`更新文档: ${currentUploadDocTitle}`"
        :document-id="currentUploadDocId"
        :document-title="currentUploadDocTitle"
        :config="uploadConfig"
        @cancel="handleUploadCancel"
        @complete="handleUploadComplete"
        @upload-success="handleUploadSuccess"
      />
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import { Component, Vue } from "nuxt-property-decorator";
import { DocumentItem } from "@/model/base";
import { KnowledgeDataItem } from "@/model/LawyerModel";
import { cascaderOptions } from "@/enum/Category";
import FileUploadModal from "@/components/common/FileUploadModal.vue";
import { downloadFileWithMessage } from "~/utils/downloadHelper";

@Component({
  components: {
    FileUploadModal,
  },
})
export default class KnowledgePage extends Vue {
  searchText = "";
  searchLoading = false;
  filterSource = "all";
  sortOrder = "desc";
  topicCategory = [];
  timeLinessFilter = "all";
  effectivenessLevelFilter = "all";
  isFavoritesMode = false;
  isAdvancedSearchVisible = false;
  listLoading = false;
  currentPage = 1;
  pageSize = 10;
  totalDocuments = 36;
  documents: KnowledgeDataItem[] = [];
  websiteOptions: Array<{ value: string; label: string }> = [];
  uploadModalVisible = false;
  currentUploadDocId = "";
  currentUploadDocTitle = "";
  get uploadConfig() {
    return {
      multiple: false,
      acceptTypes: ".doc,.docx",
      maxFileSize: 50 * 1024 * 1024,
      maxFileCount: 1,
      uploadText: "点击或拖拽文件到此区域上传",
      hintText: "支持 DOC、DOCX 格式，文件大小不超过 50MB",
      autoUpload: false,
    };
  }
  get searchButtons() {
    return [
      {
        text: "搜索",
        icon: "search",
        type: "primary",
        loading: this.searchLoading,
        isActive: false,
        handler: this.onSearch,
      },
      {
        text: "我的收藏",
        icon: "star",
        isActive: this.isFavoritesMode,
        handler: this.toggleFavorites,
      },
      {
        text: this.isAdvancedSearchVisible ? "收起筛选" : "高级筛选",
        icon: "filter",
        isActive: this.isAdvancedSearchVisible,
        handler: this.toggleAdvancedSearch,
      },
    ];
  }

  get topicCategoryOptions() {
    return cascaderOptions;
  }

  get timeLinessOptions() {
    return [
      { value: "all", label: "全部" },
      { value: "待生效", label: "待生效" },
      { value: "已生效", label: "已生效" },
      { value: "已修订", label: "已修订" },
      { value: "已废止", label: "已废止" },
    ];
  }

  get effectivenessLevelOptions() {
    return [
      { value: "all", label: "全部" },
      { value: "法律法规", label: "法律法规" },
      { value: "部门规章规范性文件", label: "部门规章规范性文件" },
      { value: "自律规则", label: "自律规则" },
      { value: "其他", label: "其他" },
    ];
  }

  async mounted() {
    await this.loadWebsiteOptions();
    this.loadDocuments();
  }

  isDocumentFavorite(doc: KnowledgeDataItem): boolean {
    return doc.isCollect || false;
  }

  async onSearch() {
    this.searchLoading = true;
    try {
      await this.loadDocuments();
    } catch (error) {
      console.error("搜索失败:", error);
    } finally {
      this.searchLoading = false;
    }
  }

  async toggleFavorites() {
    this.isFavoritesMode = !this.isFavoritesMode;
    this.currentPage = 1;
    await this.loadDocuments();
    this.$message.info(
      this.isFavoritesMode ? "已切换至收藏夹" : "已退出收藏夹模式"
    );
  }

  toggleAdvancedSearch() {
    this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
  }

  getDocActions(doc: KnowledgeDataItem) {
    const isFavorite = this.isDocumentFavorite(doc);
    return [
      {
        text: "查看",
        type: "primary",
        handler: () => this.viewDocument(doc),
      },
      {
        text: "下载",
        handler: () => this.downloadDocument(doc),
      },
      {
        type: isFavorite ? "primary" : "default",
        text: isFavorite ? "已收藏" : "收藏",
        handler: () => this.collectDocument(doc),
      },
      {
        text: "上传更新",
        class: "lawyer-btn-upload",
        handler: () => this.uploadDocument(doc.id, doc.ruleName),
      },
      {
        text: "移除",
        type: "danger",
        handler: () => this.removeDocument(doc),
      },
    ];
  }

  viewDocument(doc: KnowledgeDataItem) {
    this.$message.info(`正在打开: ${doc.ruleName}`);
    setTimeout(() => {
      const isRevoke = !!(doc.revokeDateTimestamp || doc.revokeDateStr);
      const query = {
        id: doc.id,
        ...(isRevoke ? { isRevoke: "true" } : {}),
      };
      this.$router.push({
        path: "/document",
        query,
      });
    }, 500);
  }

  async downloadDocument(doc: KnowledgeDataItem): Promise<void> {
    try {
      this.$message.loading(`正在准备下载: ${doc.ruleName}`, 0);

      const result = await this.$service.lawyer.downloadRuleFile({
        searchId: doc.id,
      });

      this.$message.destroy();

      downloadFileWithMessage(result, {
        fileName: `${doc.ruleName}.pdf`,
        showMessage: true,
        messageService: this.$message,
      });
    } catch (error) {
      this.$message.destroy();
      console.error("下载失败:", error);
      this.$message.error("下载失败，请检查网络连接后重试");
    }
  }

  async collectDocument(doc: KnowledgeDataItem) {
    const isCurrentlyFavorite = this.isDocumentFavorite(doc);
    const newCollectStatus = !isCurrentlyFavorite;
    doc.isCollect = newCollectStatus;

    try {
      const params = {
        searchId: doc.id,
        empId: this.$store.state.auth.id,
        isCollect: newCollectStatus,
      };
      console.log("🚀 ~  ~ collectDocument ~ params:", params);

      const success = await this.$service.lawyer.saveOrCancelCollect(params);

      if (success) {
        if (newCollectStatus) {
          this.$message.success(`已收藏: ${doc.ruleName}`);
        } else {
          this.$message.info(`已取消收藏: ${doc.ruleName}`);
          if (this.isFavoritesMode) {
            const index = this.documents.findIndex(
              (item) => item.id === doc.id
            );
            if (index !== -1) {
              this.documents.splice(index, 1);
              this.totalDocuments = this.documents.length;
            }
          }
        }
      } else {
        doc.isCollect = isCurrentlyFavorite;
        this.$message.error("操作失败，请重试");
      }
    } catch (error) {
      doc.isCollect = isCurrentlyFavorite;
      console.error("收藏操作失败:", error);
      this.$message.error("操作失败，请重试");
    }
  }

  uploadDocument(docId: string, docTitle: string) {
    this.uploadModalVisible = true;
    this.currentUploadDocId = docId;
    this.currentUploadDocTitle = docTitle;
  }

  handleUploadCancel() {
    this.uploadModalVisible = false;
    this.currentUploadDocId = "";
    this.currentUploadDocTitle = "";
  }

  handleUploadComplete() {
    this.uploadModalVisible = false;
    this.currentUploadDocId = "";
    this.currentUploadDocTitle = "";
  }

  handleUploadSuccess(data: any) {
    this.loadDocuments();
  }

  removeDocument(doc: KnowledgeDataItem) {
    this.$confirm({
      title: "确定要移除文档吗？",
      content: `文档"${doc.ruleName}"将被移除，此操作不可撤销。`,
      okText: "确认",
      cancelText: "取消",
      onOk: async () => {
        try {
          const success = await this.$service.lawyer.deleteRuleSource({
            id: doc.id,
          });
          if (success) {
            this.$message.success(`文档"${doc.ruleName}"已移除`);
            setTimeout(async () => {
              await this.loadDocuments();
            }, 500);
          } else {
            this.$message.error("删除失败，请重试");
          }
        } catch (error) {
          console.error("删除文档失败:", error);
          this.$message.error("删除失败，请重试");
        }
      },
    });
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.onSearch();
  }

  onShowSizeChange(current, pageSize) {
    this.pageSize = pageSize;
    this.currentPage = 1;
    this.onSearch();
  }

  getTagColor(category: string, type: string = "main"): string {
    if (!category) return "blue";
    return type === "main" ? "gold" : "blue";
  }

  async loadWebsiteOptions() {
    try {
      const websiteRatioData = await this.$service.lawyer.getWebSiteRatio();
      if (websiteRatioData && typeof websiteRatioData === "object") {
        this.websiteOptions = Object.keys(websiteRatioData).map((key) => ({
          value: key,
          label: key,
        }));
      }
    } catch (error) {
      console.error("加载网站来源数据失败:", error);
      this.websiteOptions = [];
    }
  }

  async loadDocuments() {
    this.listLoading = true;
    try {
      const params: any = {};
      if (this.searchText) {
        params.query = this.searchText;
      }
      if (this.timeLinessFilter && this.timeLinessFilter !== "all") {
        params.timeLiness = this.timeLinessFilter;
      }
      if (
        this.effectivenessLevelFilter &&
        this.effectivenessLevelFilter !== "all"
      ) {
        params.effectivenessLevel = this.effectivenessLevelFilter;
      }
      if (this.topicCategory && this.topicCategory.length > 0) {
        params.categoryMain = this.topicCategory[0];
      }
      if (this.topicCategory && this.topicCategory.length > 1) {
        params.categorySub = this.topicCategory[1];
      }
      if (this.filterSource && this.filterSource !== "all") {
        params.legalSource = this.filterSource;
      }
      if (this.sortOrder) {
        params.publishDateSort = this.sortOrder;
      }

      // 添加empId必传参数
      params.empId = this.$store.state.auth.id;

      let result;
      if (this.isFavoritesMode) {
        const collectParams = {
          empId: this.$store.state.auth.id,
        };
        result = await this.$service.lawyer.getRuleSourceCollect(collectParams);
      } else {
        result = await this.$service.lawyer.getRuleSourceList(params);
      }

      if (result && Array.isArray(result)) {
        this.documents = result;
        this.totalDocuments = result.length;
      } else {
        this.documents = [];
        this.totalDocuments = 0;
      }
    } catch (error) {
      console.error("加载文档数据失败", error);
      this.$message.error("加载数据失败，请刷新页面重试");
      this.documents = [];
      this.totalDocuments = 0;
    } finally {
      this.listLoading = false;
    }
  }

  head() {
    return {
      title: "法规与文件大家智库 - 法律合规智能系统",
    };
  }
}
</script>

<style lang="less">
.knowledge-page-wrapper {
  background-color: var(--lawyer-background);
  padding: 20px;

  .lawyer-content-wrapper {
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--lawyer-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
    background-color: var(--lawyer-surface);
  }

  .lawyer-search-form {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 24px;

    .lawyer-search-input {
      flex: 1;
      min-width: 300px;
    }

    .lawyer-search-form .ant-btn {
      flex-shrink: 0;

      &.lawyer-btn-active {
        background-color: var(--lawyer-primary);
        color: white;
        border-color: var(--lawyer-primary);
      }
    }
  }

  .lawyer-filter-options {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin-bottom: 24px;

    .lawyer-filter-group {
      flex: 1;
      flex-basis: 200px;
    }
  }

  .lawyer-loading-overlay,
  .lawyer-no-results {
    text-align: center;
    padding: 32px;
    color: var(--lawyer-text-light);
    border-radius: 8px;
    border: 1px solid var(--lawyer-border);
    margin-top: 32px;

    h3 {
      font-size: 20px;
      color: var(--lawyer-text);
      margin-bottom: 12px;
      font-weight: 500;
    }
  }

  .lawyer-document-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 32px;
  }

  .lawyer-document-item {
    padding: 24px;
    border-radius: 8px;
    border: 1px solid var(--lawyer-border);
    transition: background-color 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      background-color: rgba(var(--lawyer-primary-rgb), 0.03);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &-content {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }
  }

  .lawyer-document-icon {
    width: 40px;
    height: 40px;
    background: rgba(var(--lawyer-primary-rgb), 0.1);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--lawyer-primary);
    flex-shrink: 0;
  }

  .lawyer-document-main-content {
    flex: 1;
    min-width: 0;
  }

  .lawyer-document {
    &-header {
      margin-bottom: 12px;
    }

    &-title {
      font-size: 18px;
      font-weight: 500;
      color: var(--lawyer-text);
      margin-bottom: 8px;
      line-height: 1.4;

      a {
        text-decoration: none;
        color: inherit;
        transition: color 0.2s ease;

        &:hover {
          color: var(--lawyer-primary-dark);
        }
      }
    }

    &-meta {
      font-size: 13px;
      color: var(--lawyer-text-light);
      display: flex;
      flex-wrap: wrap;
      gap: 8px 16px;
      margin-bottom: 12px;

      .lawyer-document-meta .anticon {
        margin-right: 6px;
      }
    }

    &-summary {
      color: var(--lawyer-text-light);
      line-height: 1.6;
      margin-bottom: 16px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      flex-wrap: wrap;
      margin-top: 8px;
    }

    &-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      flex: 1;
    }

    &-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .lawyer-document-actions .ant-btn .anticon {
        font-size: 12px;
      }
    }
  }

  .lawyer-pagination {
    display: flex;
    justify-content: center;
    padding: 32px 0;
  }
}
</style>
